<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table';
	import { goto } from '$app/navigation';
	import { combineFunctionalAreas } from '$lib/utils/combineFunctionalArea';
	import { combineZonesTableView } from '$lib/utils/combineZonesTableView';
	import { CheckCircle, AlertTriangle } from 'lucide-svelte';

	let { data }: { data: PageData } = $props();

	function openImageUrl(url: string) {
		if (url) {
			window.open(url, '_blank');
		}
	}

	async function generateAllBadges() {
		// Show confirmation dialog
		const confirmed = confirm('Are you sure you want to generate badges for all Leader?');
		if (!confirmed) return;
		try {
			// Transform guests data into the format expected by the badge generator
			const badgesData = data.guests.map((guest) => ({
				imageUrl: guest.converted_url || '', // Need to switch to the converted URL #TODO
				badgeId: guest.badge_id,
				firstName: guest.first_name,
				lastName: guest.last_name,
				designation: guest.designation,
				designation2: guest.designation_2,
				category: guest.category,
				qrData: guest.qr_data, // Using badge_id as QR data
				// functionalAreas: guest.functional_area ? JSON.parse(guest.functional_area) : {},
				// zones: guest.zones || {}
				zone1: guest.zone_1,
				zone2: guest.zone_2,
				zone3: guest.zone_3,
				zone4: guest.zone_4,
				zone5: guest.zone_5,
				zone6: guest.zone_6,
				fa_acc: guest.fa_acc,
				fa_com: guest.fa_com,
				fa_crl: guest.fa_crl,
				fa_eva: guest.fa_eva,
				fa_fnp: guest.fa_fnp,
				fa_fbs: guest.fa_fbs,
				fa_gmh: guest.fa_gmh,
				fa_log: guest.fa_log,
				fa_mme: guest.fa_mme,
				fa_ogc: guest.fa_ogc,
				fa_prg: guest.fa_prg,
				fa_tml: guest.fa_tml,
				fa_wkf: guest.fa_wkf
			}));

			const response = await fetch('/private/api/generate-bulk-badges', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(badgesData)
			});

			if (!response.ok) {
				throw new Error('Failed to generate badges');
			}

			// Get the zip file blob
			const blob = await response.blob();

			// Create download link
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.download = 'leader-badges.zip';

			// Trigger download
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Cleanup
			URL.revokeObjectURL(url);
		} catch (error) {
			console.error('Error generating badges:', error);
			alert('Failed to generate badges. Please check the console for details.');
		}
	}

	async function generateSingleBadge(guest) {
		// Show confirmation dialog
		const confirmed = confirm(
			`Are you sure you want to generate badge for ${guest.first_name} ${guest.last_name}?`
		);
		if (!confirmed) return;
		try {
			// Transform single guest data into the format expected by the badge generator
			const badgeData = {
				imageUrl: guest.converted_url || '',
				badgeId: guest.badge_id,
				firstName: guest.first_name,
				lastName: guest.last_name,
				designation: guest.designation,
				designation2: guest.designation_2,
				category: guest.category,
				qrData: guest.qr_data,
				zone1: guest.zone_1,
				zone2: guest.zone_2,
				zone3: guest.zone_3,
				zone4: guest.zone_4,
				zone5: guest.zone_5,
				zone6: guest.zone_6,
				fa_acc: guest.fa_acc,
				fa_com: guest.fa_com,
				fa_crl: guest.fa_crl,
				fa_eva: guest.fa_eva,
				fa_fnp: guest.fa_fnp,
				fa_fbs: guest.fa_fbs,
				fa_gmh: guest.fa_gmh,
				fa_log: guest.fa_log,
				fa_mme: guest.fa_mme,
				fa_ogc: guest.fa_ogc,
				fa_prg: guest.fa_prg,
				fa_tml: guest.fa_tml,
				fa_wkf: guest.fa_wkf
			};

			const response = await fetch('/private/api/generate-badge', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(badgeData)
			});

			if (!response.ok) {
				throw new Error('Failed to generate badge');
			}

			// Get the PDF blob
			const blob = await response.blob();

			// Create download link
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			// Set filename using guest details
			const filename = `${guest.badge_id}_${guest.first_name}_${guest.last_name}_${guest.category}.pdf`;
			link.download = filename;

			// Trigger download
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Cleanup
			URL.revokeObjectURL(url);
		} catch (error) {
			console.error('Error generating badge:', error);
			alert('Failed to generate badge. Please check the console for details.');
		}
	}
</script>

<!-- <div class="container mx-auto p-4"> -->
<div class=" mx-auto p-4">
	<div class="flex flex-col gap-4">
		<div class="flex justify-between items-center">
			<h1 class="text-2xl font-bold">Leader Badges</h1>
			<Button onclick={generateAllBadges} variant="default">Generate All Badges</Button>
		</div>

		<div class="rounded-lg border">
			<Table.Root>
				<Table.Header class="bg-muted">
					<Table.Row>
						<Table.Head class="border-r">#</Table.Head>
						<Table.Head>Badge ID</Table.Head>
						<Table.Head>First Name</Table.Head>
						<Table.Head>Last Name</Table.Head>
						<Table.Head>Designation</Table.Head>
						<Table.Head>Designation 2</Table.Head>
						<Table.Head>QR Data</Table.Head>
						<Table.Head>Profile Image URL</Table.Head>
						<Table.Head>Functional Areas</Table.Head>
						<Table.Head>Zones</Table.Head>
						<Table.Head>Actions</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each data.guests as guest, index}
						<Table.Row>
							<Table.Cell class="border-r bg-muted">{index + 1}</Table.Cell>
							<Table.Cell>{guest.badge_id}</Table.Cell>
							<Table.Cell>{guest.first_name}</Table.Cell>
							<Table.Cell>{guest.last_name}</Table.Cell>
							<Table.Cell>{guest.designation}</Table.Cell>
							<Table.Cell>{guest.designation_2}</Table.Cell>
							<Table.Cell>{guest.qr_data}</Table.Cell>
							<Table.Cell>
								<div class="flex items-center">
									{#if guest.converted_url}
										<button
											class="text-green-600 hover:text-green-800"
											on:click={() => openImageUrl(guest.converted_url)}
											aria-label="View image"
											title="Click to open image in new tab"
										>
											<CheckCircle size={20} />
										</button>
									{:else}
										<span class="text-yellow-500" title="No image available">
											<AlertTriangle size={20} />
										</span>
									{/if}
								</div>
							</Table.Cell>
							<Table.Cell>
								{#each Object.entries(combineFunctionalAreas(guest)) as [area, value]}
									{#if value}
										<span
											class="inline-block bg-blue-100 text-blue-800 text-xs font-medium mr-1 px-2 py-0.5 rounded-full"
										>
											{area}
										</span>
									{/if}
								{/each}
							</Table.Cell>
							<Table.Cell>
								{#each Object.entries(combineZonesTableView(guest)) as [zone, value]}
									{#if value}
										<span
											class="inline-block bg-green-100 text-green-800 text-xs font-medium mr-1 px-2 py-0.5 rounded-full"
										>
											{zone}
										</span>
									{/if}
								{/each}
							</Table.Cell>

							<Table.Cell>
								<Button size="sm" onclick={() => generateSingleBadge(guest)}>Generate Badge</Button>
								<!-- Need to change the goto link -->
								<Button size="sm" onclick={() => goto(`/private/leader/edit/${guest.id}`)}
									>Edit</Button
								>
							</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		</div>
	</div>
</div>
