import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase } }) => {
    const { data: guest, error: queryError } = await supabase
        .from('workforce')
        .select('*')
        .eq('id', params.id)
        .single();

    if (queryError) {
        throw error(404, 'Guest not found');
    }

    return {
        guest
    };
};