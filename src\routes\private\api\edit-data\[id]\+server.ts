import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON>and<PERSON> } from './$types';

export const PUT: RequestHandler = async ({ params, request, locals: { supabase } }) => {
    const data = await request.json();

    console.log('Data to update: ', data);

    const { error: updateError } = await supabase
        .from('workforce')
        .update({
            first_name: data.firstName,
            last_name: data.lastName,
            designation: data.designation,
            designation_2: data.designation2,
            category: data.category,
            converted_url: data.imageUrl,
            zone_1: data.zones['1'],
            zone_2: data.zones['2'],
            zone_3: data.zones['3'],
            zone_4: data.zones['4'],
            zone_5: data.zones['5'],
            zone_6: data.zones['6'],
            fa_acc: data.functionalAreas.ACC,
            fa_com: data.functionalAreas.COM,
            fa_crl: data.functionalAreas.CRL,
            fa_eva: data.functionalAreas.EVA,
            fa_fnp: data.functionalAreas.FNP,
            fa_fbs: data.functionalAreas.FBS,
            fa_gmh: data.functionalAreas.GMH,
            fa_log: data.functionalAreas.LOG,
            fa_mme: data.functionalAreas.MME,
            fa_ogc: data.functionalAreas.OGC,
            fa_prg: data.functionalAreas.PRG,
            fa_tml: data.functionalAreas.TML,
            fa_wkf: data.functionalAreas.WKF,
            updated_at: new Date().toISOString()
        })
        .eq('badge_id', params.id);

    if (updateError) {
        return json({ error: 'Failed to update data' }, { status: 500 });
    }

    return json({ success: true });
};