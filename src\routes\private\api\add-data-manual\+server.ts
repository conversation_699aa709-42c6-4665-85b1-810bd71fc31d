import type { RequestHand<PERSON> } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    const data = await request.json();
    console.log('Data Manual add to DB: ', data);

    // const baseUrl = `https://drive.usercontent.google.com/download?id=`
    // const url = 'https://drive.google.com/open?id=1LN4Zh3JvSaY6HjaQUcQWRmtFTTQ7EaZJ'

    // // const id = url.split('id=')[1];

    // const combinedURL = `${baseUrl}${url.split('id=')[1]}`;
    // console.log('Final URL:', combinedURL);


    const zones = data.zones || {};
    // console.log('Zones: ', zones);
    const functionalAreas = data.functionalAreas || {};
    // console.log('Functional Areas: ', functionalAreas);


    const { data: insertedData, error } = await supabase.from('workforce').insert({

        first_name: data.firstName,
        last_name: data.lastName,
        designation: data.designation,
        badge_id: data.badgeId,
        qr_data: data.qrData,
        profile_url_raw: data.imageUrl,
        converted_url: data.imageUrl,
        category: data.category,
        // functional_area: data.functionalAreas,
        // zones: data.zones,
        zone_1: zones['1'] || false,
        zone_2: zones['2'] || false,
        zone_3: zones['3'] || false,
        zone_4: zones['4'] || false,
        zone_5: zones['5'] || false,
        zone_6: zones['6'] || false,
        fa_acc: functionalAreas['ACC'] || false,
        fa_com: functionalAreas['COM'] || false,
        fa_crl: functionalAreas['CRL'] || false,
        fa_eva: functionalAreas['EVA'] || false,
        fa_fnp: functionalAreas['FNP'] || false,
        fa_fbs: functionalAreas['FBS'] || false,
        fa_gmh: functionalAreas['GMH'] || false,
        fa_log: functionalAreas['LOG'] || false,
        fa_mme: functionalAreas['MME'] || false,
        fa_ogc: functionalAreas['OGC'] || false,
        fa_prg: functionalAreas['PRG'] || false,
        fa_tml: functionalAreas['TML'] || false,
        fa_wkf: functionalAreas['WKF'] || false

    }).select();

    if (error) {
        console.error('Error inserting data:', error);
        return new Response(JSON.stringify({ error: error.message }), { status: 500 });
    }
    console.log('Data inserted successfully:', insertedData);


    return new Response(
        JSON.stringify({ message: 'Data added successfully' }), { status: 200 });
    ;
}