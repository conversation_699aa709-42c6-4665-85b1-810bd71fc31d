import { supabaseAdmin } from './db/supabaseAdmin';

type RowResult = {
    row: number;
    status: 'success' | 'fail';
    message: string;
};

export async function addToDB(csvData: any[]) {

    let successCount = 0;
    let failedCount = 0;

    const processedRows: RowResult[] = [];

    console.log('CSV Data received on addToDB.ts:', csvData);


    // if (!csvData || !Array.isArray(csvData)) {
    //     return {
    //         success: false,
    //         message: 'Invalid CSV data format',

    //     };
    // }

    // if (csvData.length === 0) {
    //     return {
    //         success: false,
    //         message: 'CSV data is empty',

    //     };
    // }

    // const baseUrl = `https://drive.usercontent.google.com/download?id=`


    // Process each row
    // for (const row of csvData) {
    for (const [index, row] of csvData.entries()) {



        // let combinedURL = `${baseUrl}${(row.profileimageurl).split('id=')[1]}`;
        // console.log('Final URL:', combinedURL);

        let capitalizedFirstName = row.firstname.toUpperCase() || '';
        let capitalizedLastName = row.lastname.toUpperCase() || '';

        let capitalizedCategory = row.category.toUpperCase();
        let capitalizedDesignation = row.designationline1.toUpperCase() || '';
        let capitalizedDesignation2 = row.designationline2.toUpperCase() || '';
        // console.log('Final Cap Cat:', capitalizedCategory);

        // try {
        // perform DB insert logic here


        const { data, error } = await supabaseAdmin.from('workforce').insert({
            added_at: row.addedat,
            badge_id: row.badgeid,
            first_name: capitalizedFirstName,
            last_name: capitalizedLastName,
            designation: capitalizedDesignation,
            designation_2: capitalizedDesignation2,
            profile_url_raw: row.profileimageurl,
            converted_url: combinedURL,
            qr_data: row.qrdata,
            category: capitalizedCategory,
            zone_1: row.zone1,
            zone_2: row.zone2,
            zone_3: row.zone3,
            zone_4: row.zone4,
            zone_5: row.zone5,
            zone_6: row.zone6,
            fa_acc: row.acc,
            fa_com: row.com,
            fa_crl: row.crl,
            fa_eva: row.eva,
            fa_fnp: row.fnp,
            fa_fbs: row.fbs,
            fa_gmh: row.gmh,
            fa_log: row.log,
            fa_mme: row.mme,
            fa_ogc: row.ogc,
            fa_prg: row.prg,
            fa_tml: row.tml,
            fa_wkf: row.wkf
        });

        if (error) {
            // throw new Error(error.message);
            failedCount++;
            processedRows.push({ row: index, status: 'fail', message: error.message || 'Unknown error' });
        }




        successCount++;
        processedRows.push({ row: index, status: 'success', message: 'Inserted successfully' });
        // } catch (error) {
        //     failedCount++;
        //     processedRows.push({ row: index, status: 'fail', message: error || 'Unknown error' });
        // }
    }


    return {
        total: csvData.length,
        successCount,
        failedCount,
        rows: processedRows
    };
}

