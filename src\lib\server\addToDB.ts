
import { supabaseAdmin } from './db/supabaseAdmin';

type RowResult = {
    row: number;
    status: 'success' | 'fail';
    message: string;
};

/**
 * Splits a full name into first and last name
 * @param fullName - The full name to split
 * @returns Object with firstName and lastName properties
 */
function splitFullName(fullName: string): { firstName: string; lastName: string } {
    if (!fullName || typeof fullName !== 'string') {
        return { firstName: '', lastName: '' };
    }

    const trimmedName = fullName.trim();
    if (!trimmedName) {
        return { firstName: '', lastName: '' };
    }

    const nameParts = trimmedName.split(/\s+/);

    if (nameParts.length === 1) {
        // Only one name provided, treat as first name
        return { firstName: nameParts[0], lastName: '' };
    } else if (nameParts.length === 2) {
        // Two names: first and last
        return { firstName: nameParts[0], lastName: nameParts[1] };
    } else {
        // Multiple names: first name is the first part, last name is everything else
        const firstName = nameParts[0];
        const lastName = nameParts.slice(1).join(' ');
        return { firstName, lastName };
    }
}

export async function addToDB(csvData: any[]) {

    let successCount = 0;
    let failedCount = 0;

    const processedRows: RowResult[] = [];

    console.log('CSV Data received on addToDB.ts:', csvData);


    // if (!csvData || !Array.isArray(csvData)) {
    //     return {
    //         success: false,
    //         message: 'Invalid CSV data format',

    //     };
    // }

    // if (csvData.length === 0) {
    //     return {
    //         success: false,
    //         message: 'CSV data is empty',

    //     };
    // }

    // const baseUrl = `https://drive.usercontent.google.com/download?id=`


    // Process each row
    // for (const row of csvData) {
    for (const [index, row] of csvData.entries()) {



        // let combinedURL = `${baseUrl}${(row.profileimageurl).split('id=')[1]}`;
        // console.log('Final URL:', combinedURL);

        // Split fullname into first and last name
        const { firstName, lastName } = splitFullName(row.fullname || '');
        let capitalizedFirstName = firstName.toUpperCase();
        let capitalizedLastName = lastName.toUpperCase();

        let capitalizedCategory = row.category.toUpperCase();
        let capitalizedDesignation = row.designationline1.toUpperCase() || '';
        let capitalizedDesignation2 = row.designationline2.toUpperCase() || '';
        // console.log('Final Cap Cat:', capitalizedCategory);

        // try {
        // perform DB insert logic here


        const { data, error } = await supabaseAdmin.from('workforce').insert({
            added_at: row.addedat,
            badge_id: row.badgeid,
            first_name: capitalizedFirstName,
            last_name: capitalizedLastName,
            designation: capitalizedDesignation,
            designation_2: capitalizedDesignation2,
            profile_url_raw: row.profileimageurl,
            converted_url: combinedURL,
            qr_data: row.qrdata,
            category: capitalizedCategory,
            zone_1: row.zone1,
            zone_2: row.zone2,
            zone_3: row.zone3,
            zone_4: row.zone4,
            zone_5: row.zone5,
            zone_6: row.zone6,
            fa_acc: row.acc,
            fa_com: row.com,
            fa_crl: row.crl,
            fa_eva: row.eva,
            fa_fnp: row.fnp,
            fa_fbs: row.fbs,
            fa_gmh: row.gmh,
            fa_log: row.log,
            fa_mme: row.mme,
            fa_ogc: row.ogc,
            fa_prg: row.prg,
            fa_tml: row.tml,
            fa_wkf: row.wkf
        });

        if (error) {
            // throw new Error(error.message);
            failedCount++;
            processedRows.push({ row: index, status: 'fail', message: error.message || 'Unknown error' });
        }




        successCount++;
        processedRows.push({ row: index, status: 'success', message: 'Inserted successfully' });
        // } catch (error) {
        //     failedCount++;
        //     processedRows.push({ row: index, status: 'fail', message: error || 'Unknown error' });
        // }
    }


    return {
        total: csvData.length,
        successCount,
        failedCount,
        rows: processedRows
    };
}

