<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { PageData } from './$types';
	import { goto } from '$app/navigation';

	let { data }: { data: PageData } = $props();
	let guest = data.guest;

	let formData = {
		firstName: guest.first_name,
		lastName: guest.last_name,
		designation: guest.designation,
		badgeId: guest.badge_id,
		qrData: guest.qr_data,
		imageUrl: guest.converted_url,
		category: guest.category,
		zones: {
			1: guest.zone_1,
			2: guest.zone_2,
			3: guest.zone_3,
			4: guest.zone_4,
			5: guest.zone_5,
			6: guest.zone_6
		},
		functionalAreas: {
			ACC: guest.fa_acc,
			COM: guest.fa_com,
			CRL: guest.fa_crl,
			EVA: guest.fa_eva,
			FNP: guest.fa_fnp,
			FBS: guest.fa_fbs,
			GMH: guest.fa_gmh,
			LOG: guest.fa_log,
			MME: guest.fa_mme,
			OGC: guest.fa_ogc,
			PRG: guest.fa_prg,
			TML: guest.fa_tml,
			WKF: guest.fa_wkf
		}
	};

	async function handleSubmit() {
		try {
			const response = await fetch(`/private/api/edit-data/${guest.badge_id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			if (!response.ok) {
				throw new Error('Failed to update data');
			}

			goto('/private/leader');
		} catch (error) {
			console.error('Error updating data:', error);
			alert('Failed to update data');
		}
	}
</script>

<div class="container mx-auto p-4 max-w-2xl">
	<h1 class="text-2xl font-bold mb-6">Edit Guest Details</h1>

	<form on:submit|preventDefault={handleSubmit} class="space-y-6">
		<div class="grid grid-cols-2 gap-4">
			<div class="space-y-2">
				<label for="firstName" class="block font-medium">First Name</label>
				<input
					id="firstName"
					type="text"
					bind:value={formData.firstName}
					class="w-full p-2 border rounded"
					required
				/>
			</div>

			<div class="space-y-2">
				<label for="lastName" class="block font-medium">Last Name</label>
				<input
					id="lastName"
					type="text"
					bind:value={formData.lastName}
					class="w-full p-2 border rounded"
					required
				/>
			</div>

			<div class="space-y-2">
				<label for="designation" class="block font-medium">Designation</label>
				<input
					id="designation"
					type="text"
					bind:value={formData.designation}
					class="w-full p-2 border rounded"
					required
				/>
			</div>

			<div class="space-y-2">
				<label for="badgeId" class="block font-medium">Badge ID</label>
				<input
					id="badgeId"
					type="text"
					bind:value={formData.badgeId}
					class="w-full p-2 border rounded"
				/>
			</div>
			<div class="space-y-2">
				<label for="qrData" class="block font-medium">QR Data</label>
				<input
					id="qrData"
					type="text"
					bind:value={formData.qrData}
					class="w-full p-2 border rounded"
				/>
			</div>

			<div class="space-y-2">
				<label for="imageUrl" class="block font-medium">Image URL</label>
				<input
					id="imageUrl"
					type="url"
					bind:value={formData.imageUrl}
					class="w-full p-2 border rounded"
					required
				/>
			</div>
		</div>

		<div class="space-y-4">
			<h2 class="text-lg font-semibold">Zones</h2>
			<div class="grid grid-cols-3 gap-4">
				{#each Object.entries(formData.zones) as [zone, value]}
					<label class="flex items-center space-x-2">
						<input type="checkbox" bind:checked={formData.zones[zone]} />
						<span>Zone {zone}</span>
					</label>
				{/each}
			</div>
		</div>

		<div class="space-y-4">
			<h2 class="text-lg font-semibold">Functional Areas</h2>
			<div class="grid grid-cols-3 gap-4">
				{#each Object.entries(formData.functionalAreas) as [area, value]}
					<label class="flex items-center space-x-2">
						<input type="checkbox" bind:checked={formData.functionalAreas[area]} />
						<span>{area}</span>
					</label>
				{/each}
			</div>
		</div>

		<div class="flex space-x-4">
			<Button type="submit">Save Changes</Button>
			<Button type="button" variant="outline" onclick={() => goto('/private/leader')}>
				Cancel
			</Button>
		</div>
	</form>
</div>
